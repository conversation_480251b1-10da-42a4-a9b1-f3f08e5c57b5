/* UserProfileCard 响应式样式 */

/* 基础样式 */
.userProfileCardResponsive :global(.ant-card-body) {
  padding: 24px !important;
}

.actionButtonsResponsive {
  position: absolute !important;
  z-index: 20 !important;
  transition: all 0.2s ease !important;
}

.actionButtonsResponsive :global(.ant-btn) {
  transition: all 0.2s ease !important;
}

/* 平板设备 (max-width: 1024px) */
@media (max-width: 1024px) {
  .userProfileCardResponsive :global(.ant-card-body) {
    padding: 20px !important;
  }
  
  .userProfileContentResponsive {
    gap: 24px !important;
  }
  
  .statsSectionResponsive {
    min-width: 180px !important;
  }
  
  .activitySectionResponsive {
    min-width: 220px !important;
  }
}

/* 移动设备 (max-width: 768px) */
@media (max-width: 768px) {
  .userProfileCardResponsive :global(.ant-card-body) {
    padding: 16px !important;
  }
  
  .userProfileContentResponsive {
    flex-direction: column !important;
    gap: 20px !important;
    align-items: stretch !important;
  }
  
  .userInfoSectionResponsive {
    flex: 1 1 auto !important;
    min-width: auto !important;
    justify-content: center !important;
  }
  
  .statsSectionResponsive {
    flex: 1 1 auto !important;
    min-width: auto !important;
    order: 2 !important;
  }
  
  .activitySectionResponsive {
    flex: 1 1 auto !important;
    min-width: auto !important;
    padding-left: 0 !important;
    order: 3 !important;
  }
  
  .dividerResponsive {
    display: none !important;
  }
  
  .statsGridResponsive {
    justify-content: space-between !important;
  }
}

/* 小屏幕移动设备 (max-width: 480px) */
@media (max-width: 480px) {
  .userProfileCardResponsive :global(.ant-card-body) {
    padding: 12px !important;
  }
  
  .userAvatarResponsive {
    margin-right: 12px !important;
  }
  
  .statsGridResponsive {
    gap: 4px !important;
  }
  
  .statsGridResponsive > div {
    min-width: 50px !important;
  }
  
  .actionButtonsResponsive {
    top: 8px !important;
    right: 8px !important;
  }
  
  .actionButtonsResponsive :global(.ant-btn) {
    width: 32px !important;
    height: 32px !important;
    min-width: 32px !important;
  }
}

/* 超小屏幕设备 (max-width: 360px) */
@media (max-width: 360px) {
  .userProfileCardResponsive :global(.ant-card-body) {
    padding: 8px !important;
  }
  
  .actionButtonsResponsive {
    top: 6px !important;
    right: 6px !important;
  }
  
  .actionButtonsResponsive :global(.ant-btn) {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
  }
  
  .userAvatarResponsive {
    margin-right: 8px !important;
  }
}
