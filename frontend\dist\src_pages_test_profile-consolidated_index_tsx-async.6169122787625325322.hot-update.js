globalThis.makoModuleHotUpdate('src/pages/test/profile-consolidated/index.tsx', {
    modules: {
        "src/pages/test/profile-consolidated/UserProfileCard.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var _services = __mako_require__("src/services/index.ts");
            var _max = __mako_require__("src/.umi/exports.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const { Step } = _antd.Steps;
            const UserProfileCard = ()=>{
                var _subscriptionPlans_find;
                _s();
                // 用户详细信息状态
                const [userInfo, setUserInfo] = (0, _react.useState)({
                    name: "",
                    position: "",
                    email: "",
                    phone: "",
                    telephone: "",
                    registerDate: "",
                    lastLoginTime: "",
                    lastLoginTeam: "",
                    teamCount: 0,
                    avatar: ""
                });
                const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
                const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
                // 个人统计数据状态
                const [personalStats, setPersonalStats] = (0, _react.useState)({
                    vehicles: 0,
                    personnel: 0,
                    warnings: 0,
                    alerts: 0
                });
                const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
                const [statsError, setStatsError] = (0, _react.useState)(null);
                // 订阅计划数据
                const subscriptionPlans = [
                    {
                        id: "basic",
                        name: "基础版",
                        price: 0,
                        description: "适合小团队使用",
                        features: [
                            "最多5个团队",
                            "最多20辆车辆",
                            "基础安全监控",
                            "基本报告功能"
                        ]
                    },
                    {
                        id: "professional",
                        name: "专业版",
                        price: 199,
                        description: "适合中小型企业",
                        features: [
                            "最多20个团队",
                            "最多100辆车辆",
                            "高级安全监控",
                            "详细分析报告",
                            "设备状态预警",
                            "优先技术支持"
                        ]
                    },
                    {
                        id: "enterprise",
                        name: "企业版",
                        price: 499,
                        description: "适合大型企业",
                        features: [
                            "不限团队数量",
                            "不限车辆数量",
                            "AI安全分析",
                            "实时监控告警",
                            "定制化报告",
                            "专属客户经理",
                            "24/7技术支持"
                        ]
                    }
                ];
                // 当前订阅信息
                const currentSubscription = {
                    planId: "basic",
                    expires: "2025-12-31"
                };
                // 状态管理
                const [editProfileModalVisible, setEditProfileModalVisible] = (0, _react.useState)(false);
                const [subscriptionModalVisible, setSubscriptionModalVisible] = (0, _react.useState)(false);
                const [logoutModalVisible, setLogoutModalVisible] = (0, _react.useState)(false);
                const [logoutLoading, setLogoutLoading] = (0, _react.useState)(false);
                const [currentStep, setCurrentStep] = (0, _react.useState)(0);
                const [editProfileForm] = _antd.Form.useForm();
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                // 获取用户数据
                (0, _react.useEffect)(()=>{
                    console.log('UserProfileCard: useEffect 开始执行');
                    const fetchUserData = async ()=>{
                        try {
                            console.log('UserProfileCard: 开始获取用户数据');
                            // 分别获取用户详细信息和统计数据，避免一个失败影响另一个
                            const userDetailPromise = _user.UserService.getUserProfileDetail().catch((error)=>{
                                console.error('获取用户详细信息失败:', error);
                                setUserInfoError('获取用户详细信息失败，请稍后重试');
                                return null;
                            });
                            const statsPromise = _user.UserService.getUserPersonalStats().catch((error)=>{
                                console.error('获取统计数据失败:', error);
                                setStatsError('获取统计数据失败，请稍后重试');
                                return null;
                            });
                            const [userDetail, stats] = await Promise.all([
                                userDetailPromise,
                                statsPromise
                            ]);
                            if (userDetail) {
                                console.log('UserProfileCard: 获取到用户详细信息:', userDetail);
                                setUserInfo(userDetail);
                                setUserInfoError(null);
                            }
                            if (stats) {
                                console.log('UserProfileCard: 获取到统计数据:', stats);
                                setPersonalStats(stats);
                                setStatsError(null);
                            }
                        } catch (error) {
                            console.error('获取用户数据时发生未知错误:', error);
                            setUserInfoError('获取用户数据失败，请刷新页面重试');
                            setStatsError('获取统计数据失败，请刷新页面重试');
                        } finally{
                            setUserInfoLoading(false);
                            setStatsLoading(false);
                        }
                    };
                    fetchUserData();
                }, []);
                // 退出登录处理函数
                const handleLogout = async ()=>{
                    try {
                        setLogoutLoading(true);
                        // 调用退出登录API
                        await _services.AuthService.logout();
                        // 清除 initialState
                        if (setInitialState) await setInitialState({
                            currentUser: undefined,
                            currentTeam: undefined
                        });
                        // 跳转到登录页面
                        _max.history.push('/user/login');
                    } catch (error) {
                        console.error('退出登录失败:', error);
                        // 即使API调用失败，也要清除本地状态并跳转
                        if (setInitialState) await setInitialState({
                            currentUser: undefined,
                            currentTeam: undefined
                        });
                        _max.history.push('/user/login');
                    } finally{
                        setLogoutLoading(false);
                        setLogoutModalVisible(false);
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            className: "dashboard-card",
                            styles: {
                                body: {
                                    padding: 32
                                }
                            },
                            children: userInfoError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                message: "用户信息加载失败",
                                description: userInfoError,
                                type: "error",
                                showIcon: true,
                                style: {
                                    marginBottom: 24
                                }
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 217,
                                columnNumber: 11
                            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                spinning: userInfoLoading,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                    className: "user-profile-card-responsive",
                                    style: {
                                        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                                        borderRadius: 16,
                                        color: "white",
                                        position: "relative",
                                        overflow: "hidden",
                                        minHeight: 140,
                                        border: "none"
                                    },
                                    styles: {
                                        body: {
                                            padding: 24,
                                            height: "100%"
                                        }
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            style: {
                                                position: "absolute",
                                                top: 16,
                                                right: 16,
                                                zIndex: 20
                                            },
                                            size: 8,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                    title: "设置",
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                                        menu: {
                                                            items: [
                                                                {
                                                                    key: "editProfile",
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 256,
                                                                        columnNumber: 33
                                                                    }, void 0),
                                                                    label: "修改资料",
                                                                    onClick: ()=>{
                                                                        setEditProfileModalVisible(true);
                                                                        setCurrentStep(0);
                                                                        editProfileForm.setFieldsValue({
                                                                            name: userInfo.name,
                                                                            email: userInfo.email,
                                                                            telephone: userInfo.phone || userInfo.telephone
                                                                        });
                                                                    }
                                                                },
                                                                {
                                                                    key: "subscription",
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TagOutlined, {}, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 270,
                                                                        columnNumber: 33
                                                                    }, void 0),
                                                                    label: "订阅套餐",
                                                                    onClick: ()=>setSubscriptionModalVisible(true)
                                                                }
                                                            ]
                                                        },
                                                        trigger: [
                                                            "click"
                                                        ],
                                                        placement: "bottomRight",
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                            type: "text",
                                                            shape: "circle",
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 282,
                                                                columnNumber: 29
                                                            }, void 0),
                                                            style: {
                                                                color: "rgba(255,255,255,0.9)",
                                                                backgroundColor: "rgba(255,255,255,0.15)",
                                                                border: "none",
                                                                transition: "all 0.2s"
                                                            },
                                                            onMouseEnter: (e)=>{
                                                                e.currentTarget.style.backgroundColor = "rgba(255,255,255,0.25)";
                                                                e.currentTarget.style.color = "white";
                                                            },
                                                            onMouseLeave: (e)=>{
                                                                e.currentTarget.style.backgroundColor = "rgba(255,255,255,0.15)";
                                                                e.currentTarget.style.color = "rgba(255,255,255,0.9)";
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 279,
                                                            columnNumber: 21
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 251,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 250,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                    title: "退出登录",
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                        type: "text",
                                                        shape: "circle",
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LogoutOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 305,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        onClick: ()=>setLogoutModalVisible(true),
                                                        style: {
                                                            color: "rgba(255,255,255,0.9)",
                                                            backgroundColor: "rgba(255,255,255,0.15)",
                                                            border: "none",
                                                            transition: "all 0.2s"
                                                        },
                                                        onMouseEnter: (e)=>{
                                                            e.currentTarget.style.backgroundColor = "rgba(255,77,79,0.3)";
                                                            e.currentTarget.style.color = "#ff4d4f";
                                                        },
                                                        onMouseLeave: (e)=>{
                                                            e.currentTarget.style.backgroundColor = "rgba(255,255,255,0.15)";
                                                            e.currentTarget.style.color = "rgba(255,255,255,0.9)";
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 302,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 301,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 241,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                position: "absolute",
                                                top: -25,
                                                right: -25,
                                                width: 100,
                                                height: 100,
                                                background: "rgba(255,255,255,0.1)",
                                                borderRadius: "50%"
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 325,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                position: "absolute",
                                                bottom: -30,
                                                left: -30,
                                                width: 80,
                                                height: 80,
                                                background: "rgba(255,255,255,0.05)",
                                                borderRadius: "50%"
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 336,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                position: "absolute",
                                                top: "50%",
                                                right: "20%",
                                                width: 60,
                                                height: 60,
                                                background: "rgba(255,255,255,0.03)",
                                                borderRadius: "50%",
                                                transform: "translateY(-50%)"
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 347,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            gap: 32,
                                            style: {
                                                position: "relative",
                                                zIndex: 1,
                                                width: "100%"
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                    align: "center",
                                                    style: {
                                                        flex: "0 0 320px",
                                                        minWidth: 320
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                            size: 64,
                                                            shape: "square",
                                                            style: {
                                                                backgroundColor: "rgba(255,255,255,0.2)",
                                                                marginRight: 20,
                                                                fontSize: 24,
                                                                fontWeight: 600,
                                                                border: "2px solid rgba(255,255,255,0.3)"
                                                            },
                                                            children: userInfo.name ? userInfo.name.charAt(0).toUpperCase() : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 380,
                                                                columnNumber: 78
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 369,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            direction: "vertical",
                                                            size: 4,
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                    level: 3,
                                                                    style: {
                                                                        margin: 0,
                                                                        color: "white",
                                                                        fontSize: 22,
                                                                        fontWeight: 600
                                                                    },
                                                                    children: userInfo.name || "加载中..."
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 385,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                    direction: "vertical",
                                                                    size: 4,
                                                                    children: [
                                                                        userInfo.email && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                            size: 6,
                                                                            align: "center",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                                                    style: {
                                                                                        fontSize: 13,
                                                                                        color: "rgba(255,255,255,0.9)"
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                    lineNumber: 401,
                                                                                    columnNumber: 27
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    style: {
                                                                                        color: "rgba(255,255,255,0.9)",
                                                                                        fontSize: 12
                                                                                    },
                                                                                    children: userInfo.email
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                    lineNumber: 402,
                                                                                    columnNumber: 27
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 400,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        (userInfo.phone || userInfo.telephone) && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                            size: 6,
                                                                            align: "center",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                                                                    style: {
                                                                                        fontSize: 13,
                                                                                        color: "rgba(255,255,255,0.9)"
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                    lineNumber: 409,
                                                                                    columnNumber: 27
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    style: {
                                                                                        color: "rgba(255,255,255,0.9)",
                                                                                        fontSize: 12
                                                                                    },
                                                                                    children: userInfo.phone || userInfo.telephone
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                    lineNumber: 410,
                                                                                    columnNumber: 27
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 408,
                                                                            columnNumber: 25
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 398,
                                                                    columnNumber: 21
                                                                }, this),
                                                                userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: "rgba(255,255,255,0.8)",
                                                                        fontWeight: 500
                                                                    },
                                                                    children: [
                                                                        "注册于 ",
                                                                        userInfo.registerDate
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 419,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 384,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 367,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                                    type: "vertical",
                                                    style: {
                                                        height: "80px",
                                                        borderColor: "rgba(255,255,255,0.3)",
                                                        margin: "0 16px"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 427,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                    vertical: true,
                                                    style: {
                                                        flex: "1 1 auto"
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            align: "center",
                                                            style: {
                                                                justifyContent: "center",
                                                                marginBottom: 30,
                                                                height: 20
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BarChartOutlined, {
                                                                    style: {
                                                                        fontSize: 16,
                                                                        color: "rgba(255,255,255,0.9)"
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 452,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        color: "rgba(255,255,255,0.9)",
                                                                        fontSize: 14,
                                                                        fontWeight: 600,
                                                                        lineHeight: 1
                                                                    },
                                                                    children: "数据概览"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 458,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 444,
                                                            columnNumber: 19
                                                        }, this),
                                                        statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            style: {
                                                                fontSize: 12,
                                                                color: "rgba(255,255,255,0.8)"
                                                            },
                                                            children: "数据加载失败"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 464,
                                                            columnNumber: 21
                                                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                                            spinning: statsLoading,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                justify: "space-around",
                                                                align: "center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            textAlign: 'center'
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 20,
                                                                                    fontWeight: 700,
                                                                                    color: "white",
                                                                                    lineHeight: 1
                                                                                },
                                                                                children: personalStats.vehicles
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 472,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 13,
                                                                                    color: "rgba(255,255,255,0.8)",
                                                                                    marginTop: 3
                                                                                },
                                                                                children: "车辆"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 480,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 471,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            textAlign: 'center'
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 20,
                                                                                    fontWeight: 700,
                                                                                    color: "white",
                                                                                    lineHeight: 1
                                                                                },
                                                                                children: personalStats.personnel
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 489,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 13,
                                                                                    color: "rgba(255,255,255,0.8)",
                                                                                    marginTop: 3
                                                                                },
                                                                                children: "人员"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 497,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 488,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            textAlign: 'center'
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 20,
                                                                                    fontWeight: 700,
                                                                                    color: "white",
                                                                                    lineHeight: 1
                                                                                },
                                                                                children: personalStats.warnings
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 506,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 13,
                                                                                    color: "rgba(255,255,255,0.8)",
                                                                                    marginTop: 3
                                                                                },
                                                                                children: "预警"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 514,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 505,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            textAlign: 'center'
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 20,
                                                                                    fontWeight: 700,
                                                                                    color: "white",
                                                                                    lineHeight: 1
                                                                                },
                                                                                children: personalStats.alerts
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 523,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 13,
                                                                                    color: "rgba(255,255,255,0.8)",
                                                                                    marginTop: 3
                                                                                },
                                                                                children: "告警"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 531,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 522,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 470,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 468,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 437,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                                    type: "vertical",
                                                    style: {
                                                        height: "80px",
                                                        borderColor: "rgba(255,255,255,0.3)",
                                                        margin: "0 16px"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 545,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    direction: "vertical",
                                                    size: 10,
                                                    style: {
                                                        flex: "0 0 300px",
                                                        paddingLeft: 20,
                                                        width: 300,
                                                        minWidth: 300
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            direction: "vertical",
                                                            size: 4,
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        fontSize: 12,
                                                                        color: "rgba(255,255,255,0.8)",
                                                                        fontWeight: 500
                                                                    },
                                                                    children: "最后登录时间"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 566,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        fontSize: 14,
                                                                        color: "white",
                                                                        fontWeight: 600,
                                                                        lineHeight: 1.3
                                                                    },
                                                                    children: userInfo.lastLoginTime || "暂无记录"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 569,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 565,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            direction: "vertical",
                                                            size: 4,
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        fontSize: 12,
                                                                        color: "rgba(255,255,255,0.8)",
                                                                        fontWeight: 500
                                                                    },
                                                                    children: "最后登录团队"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 574,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        fontSize: 14,
                                                                        color: "white",
                                                                        fontWeight: 600,
                                                                        lineHeight: 1.3
                                                                    },
                                                                    children: userInfo.lastLoginTeam || "暂无记录"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 577,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 573,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 555,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 361,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 227,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 225,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 210,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "修改个人资料",
                            open: editProfileModalVisible,
                            onCancel: ()=>{
                                setEditProfileModalVisible(false);
                                setCurrentStep(0);
                            },
                            footer: [
                                currentStep === 1 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    onClick: ()=>setCurrentStep(0),
                                    children: "上一步"
                                }, "back", false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 598,
                                    columnNumber: 13
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    onClick: ()=>{
                                        if (currentStep === 0) editProfileForm.validateFields().then(()=>{
                                            setCurrentStep(1);
                                        });
                                        else editProfileForm.validateFields().then((values)=>{
                                            console.log("个人资料表单值:", values);
                                            // 提交表单，这里简化处理，只输出到控制台
                                            setEditProfileModalVisible(false);
                                            setCurrentStep(0);
                                        });
                                    },
                                    children: currentStep === 0 ? "下一步" : "确定"
                                }, "submit", false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 602,
                                    columnNumber: 11
                                }, void 0)
                            ],
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Steps, {
                                    current: currentStep,
                                    style: {
                                        marginBottom: 16
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Step, {
                                            title: "填写信息"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 625,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Step, {
                                            title: "安全验证"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 626,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 624,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                    form: editProfileForm,
                                    layout: "vertical",
                                    requiredMark: false,
                                    children: currentStep === 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "name",
                                                label: "用户名",
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: "请输入用户名"
                                                    }
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "请输入用户名"
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 637,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 632,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "email",
                                                label: "邮箱",
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: "请输入邮箱地址"
                                                    },
                                                    {
                                                        type: "email",
                                                        message: "请输入有效的邮箱地址"
                                                    }
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "请输入邮箱地址"
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 647,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 639,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "telephone",
                                                label: "手机号",
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: "请输入手机号"
                                                    },
                                                    {
                                                        pattern: /^1\d{10}$/,
                                                        message: "请输入有效的手机号"
                                                    }
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "请输入手机号"
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 657,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 649,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true) : /* 验证码步骤 - 使用 Space 组件 */ /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        direction: "vertical",
                                        align: "center",
                                        style: {
                                            width: "100%"
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                style: {
                                                    margin: "12px 0"
                                                },
                                                children: [
                                                    "验证码已发送至您的手机号",
                                                    " ",
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        strong: true,
                                                        children: editProfileForm.getFieldValue("telephone")
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 665,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 663,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "verificationCode",
                                                label: "验证码",
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: "请输入验证码"
                                                    }
                                                ],
                                                style: {
                                                    textAlign: "center"
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "请输入6位验证码",
                                                    maxLength: 6,
                                                    style: {
                                                        width: "50%",
                                                        textAlign: "center"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 673,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 667,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "link",
                                                style: {
                                                    padding: 0
                                                },
                                                children: "重新发送验证码"
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 679,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 662,
                                        columnNumber: 13
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 629,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 589,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "订阅套餐",
                            open: subscriptionModalVisible,
                            onCancel: ()=>setSubscriptionModalVisible(false),
                            footer: null,
                            width: 800,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                    size: "small",
                                    style: {
                                        background: "#f9f9f9",
                                        marginBottom: 16
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        justify: "space-between",
                                        align: "center",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                strong: true,
                                                children: "当前套餐: "
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 704,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                color: "green",
                                                style: {
                                                    marginLeft: 8,
                                                    fontSize: 13
                                                },
                                                children: (_subscriptionPlans_find = subscriptionPlans.find((p)=>p.id === currentSubscription.planId)) === null || _subscriptionPlans_find === void 0 ? void 0 : _subscriptionPlans_find.name
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 705,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                children: [
                                                    "到期时间: ",
                                                    currentSubscription.expires
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 712,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 703,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 696,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    gutter: 24,
                                    children: subscriptionPlans.map((plan)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            span: 8,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                style: {
                                                    height: "100%",
                                                    border: `1px solid ${plan.id === currentSubscription.planId ? "#52c41a" : "#d9d9d9"}`,
                                                    position: "relative"
                                                },
                                                styles: {
                                                    body: {
                                                        padding: 16
                                                    }
                                                },
                                                children: [
                                                    plan.id === currentSubscription.planId && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                        color: "green",
                                                        style: {
                                                            position: "absolute",
                                                            top: -10,
                                                            right: -10,
                                                            borderRadius: 2,
                                                            boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
                                                        },
                                                        children: "当前套餐"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 735,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                        level: 4,
                                                        style: {
                                                            textAlign: "center",
                                                            margin: "12px 0 8px"
                                                        },
                                                        children: plan.name
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 748,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        align: "center",
                                                        style: {
                                                            marginBottom: 12
                                                        },
                                                        children: [
                                                            plan.price > 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                        level: 2,
                                                                        style: {
                                                                            marginBottom: 0
                                                                        },
                                                                        children: [
                                                                            "¥",
                                                                            plan.price
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 757,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        children: "/月"
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 760,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                level: 2,
                                                                style: {
                                                                    color: "#52c41a",
                                                                    marginBottom: 0
                                                                },
                                                                children: "免费"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 763,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                type: "secondary",
                                                                style: {
                                                                    marginTop: 4
                                                                },
                                                                children: plan.description
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 770,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 754,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                        direction: "vertical",
                                                        size: 6,
                                                        style: {
                                                            minHeight: 170,
                                                            width: "100%"
                                                        },
                                                        children: plan.features.map((feature, index)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                align: "start",
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                        style: {
                                                                            color: "#52c41a",
                                                                            marginTop: 4
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 781,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        children: feature
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 787,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, index, true, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 780,
                                                                columnNumber: 21
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 778,
                                                        columnNumber: 17
                                                    }, this),
                                                    plan.id !== currentSubscription.planId ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                        type: "primary",
                                                        block: true,
                                                        style: {
                                                            marginTop: 12,
                                                            boxShadow: "0 2px 8px rgba(24, 144, 255, 0.3)"
                                                        },
                                                        onClick: ()=>{
                                                            console.log("选择套餐:", plan);
                                                            setSubscriptionModalVisible(false);
                                                        },
                                                        children: "立即订阅"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 793,
                                                        columnNumber: 19
                                                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                        block: true,
                                                        style: {
                                                            marginTop: 12,
                                                            background: "#f6ffed",
                                                            borderColor: "#b7eb8f",
                                                            color: "#389e0d"
                                                        },
                                                        disabled: true,
                                                        children: "当前套餐"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 808,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 722,
                                                columnNumber: 15
                                            }, this)
                                        }, plan.id, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 720,
                                            columnNumber: 13
                                        }, this))
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 718,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    justify: "center",
                                    style: {
                                        marginTop: 20
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "订阅服务自动续费，可随时取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 827,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 826,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 688,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "确认退出登录",
                            open: logoutModalVisible,
                            onCancel: ()=>setLogoutModalVisible(false),
                            footer: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    onClick: ()=>setLogoutModalVisible(false),
                                    children: "取消"
                                }, "cancel", false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 837,
                                    columnNumber: 11
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    danger: true,
                                    loading: logoutLoading,
                                    onClick: handleLogout,
                                    children: "确认退出"
                                }, "confirm", false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 840,
                                    columnNumber: 11
                                }, void 0)
                            ],
                            width: 400,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                direction: "vertical",
                                align: "center",
                                style: {
                                    width: "100%",
                                    padding: "20px 0"
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LogoutOutlined, {
                                        style: {
                                            fontSize: 48,
                                            color: '#ff4d4f'
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 854,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        style: {
                                            fontSize: 16
                                        },
                                        children: "您确定要退出登录吗？"
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 855,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        style: {
                                            textAlign: "center"
                                        },
                                        children: "退出后您需要重新登录才能继续使用系统"
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 856,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 853,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 832,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true);
            };
            _s(UserProfileCard, "2csI36bGbcIUYJyMcENjxkdXqJY=", false, function() {
                return [
                    _antd.Form.useForm,
                    _max.useModel
                ];
            });
            _c = UserProfileCard;
            var _default = UserProfileCard;
            var _c;
            $RefreshReg$(_c, "UserProfileCard");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15927141442072172682';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/test/profile-consolidated/index.tsx": [
            "src/pages/test/profile-consolidated/index.tsx"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_test_profile-consolidated_index_tsx-async.6169122787625325322.hot-update.js.map